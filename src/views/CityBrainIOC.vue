<template>
  <div class="bg-gradient-to-b from-dark to-dark-light text-gray-100 min-h-screen font-sans overflow-x-hidden">
    <!-- 背景装饰 -->
    <div class="fixed inset-0 z-0 opacity-10">
      <div class="absolute top-0 left-0 w-full h-full bg-[url('https://picsum.photos/id/1/1920/1080')] bg-cover bg-center"></div>
      <div class="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_center,rgba(14,165,233,0.2),transparent_70%)]"></div>
    </div>

    <!-- 顶部状态栏 -->
    <header class="relative z-10 glass border-b border-gray-700/50">
      <div class="container mx-auto px-4 py-3 flex justify-between items-center">
        <div class="flex items-center space-x-2">
          <i class="fa fa-brain text-primary text-xl glow"></i>
          <span class="font-semibold">城市大脑IOC系统</span>
        </div>
        
        <div class="hidden md:flex items-center space-x-6">
          <div class="flex items-center space-x-2">
            <span class="text-xs text-gray-400">系统状态</span>
            <span class="w-2 h-2 rounded-full bg-green-500 animate-pulse"></span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-xs text-gray-400">在线设备</span>
            <span class="text-xs">24,587</span>
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-xs text-gray-400">数据传输</span>
            <span class="text-xs">12.8 GB/s</span>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <div class="text-xs bg-dark-light px-3 py-1 rounded-full">
            <span class="text-primary">实时监控中</span>
          </div>
          <div class="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
            <i class="fa fa-user text-primary"></i>
          </div>
        </div>
      </div>
    </header>

    <!-- 主标题 -->
    <div class="relative z-10 container mx-auto px-4 py-8 text-center">
      <h1 class="text-[clamp(2rem,6vw,3.5rem)] font-bold text-transparent bg-clip-text bg-gradient-to-r from-primary via-secondary to-tertiary text-shadow">
        城市大脑智能运行中心
      </h1>
      <p class="mt-4 text-gray-300 max-w-2xl mx-auto text-[clamp(1rem,2vw,1.2rem)]">
        基于人体神经网络架构的城市智能管理系统 — 感知、分析、决策、执行
      </p>
    </div>

    <!-- 主要内容区 -->
    <main class="relative z-10 container mx-auto px-4 pb-16">
      <!-- 大脑核心区域 -->
      <section class="relative mb-20 flex justify-center">
        <div class="relative w-full max-w-2xl aspect-square max-h-[60vh]">
          <!-- 大脑容器 -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="relative w-[90%] h-[90%] rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 animate-float overflow-hidden border border-primary/30">
              <!-- 大脑纹理 -->
              <div class="absolute inset-0 bg-[url('https://picsum.photos/id/100/600/600')] bg-cover bg-center opacity-10 mix-blend-overlay"></div>
              
              <!-- 核心标识 -->
              <div class="absolute inset-0 flex flex-col items-center justify-center">
                <i class="fa fa-brain text-[clamp(6rem,20vw,10rem)] text-primary/60 animate-pulse-slow glow"></i>
                <div class="mt-4 text-center">
                  <h2 class="text-[clamp(1.5rem,4vw,2.5rem)] font-bold text-white text-shadow">城市大脑</h2>
                  <p class="text-primary/80 text-sm mt-1">神经中枢系统</p>
                </div>
              </div>
              
              <!-- 神经网络连接线 -->
              <svg class="absolute inset-0 w-full h-full" xmlns="http://www.w3.org/2000/svg">
                <path d="M20%,20% Q50%,0% 80%,20% T90%,80%" stroke="rgba(14, 165, 233, 0.4)" stroke-width="1.5" fill="none" class="brain-connection" />
                <path d="M10%,50% Q50%,30% 90%,50%" stroke="rgba(139, 92, 246, 0.4)" stroke-width="1.5" fill="none" class="brain-connection" />
                <path d="M20%,80% Q50%,100% 80%,80%" stroke="rgba(16, 185, 129, 0.4)" stroke-width="1.5" fill="none" class="brain-connection" />
                <path d="M30%,30% Q50%,50% 70%,30% T80%,70%" stroke="rgba(245, 158, 11, 0.4)" stroke-width="1.5" fill="none" class="brain-connection" />
              </svg>
              
              <!-- 活动节点 -->
              <div class="absolute top-[30%] left-[25%] w-3 h-3 bg-primary rounded-full animate-pulse"></div>
              <div class="absolute top-[45%] left-[70%] w-3 h-3 bg-secondary rounded-full animate-pulse" style="animation-delay: 0.5s"></div>
              <div class="absolute top-[65%] left-[35%] w-3 h-3 bg-tertiary rounded-full animate-pulse" style="animation-delay: 1s"></div>
              <div class="absolute top-[20%] left-[60%] w-3 h-3 bg-quaternary rounded-full animate-pulse" style="animation-delay: 1.5s"></div>
            </div>
          </div>
          
          <!-- 大脑性能指标 -->
          <div class="absolute -bottom-6 left-1/2 transform -translate-x-1/2 glass rounded-full px-6 py-2 border border-gray-700/50 flex items-center space-x-6">
            <div class="flex flex-col items-center">
              <span class="text-xs text-gray-400">CPU使用率</span>
              <span class="text-primary font-semibold">37%</span>
            </div>
            <div class="flex flex-col items-center">
              <span class="text-xs text-gray-400">内存占用</span>
              <span class="text-secondary font-semibold">52%</span>
            </div>
            <div class="flex flex-col items-center">
              <span class="text-xs text-gray-400">响应时间</span>
              <span class="text-tertiary font-semibold">12ms</span>
            </div>
            <div class="flex flex-col items-center">
              <span class="text-xs text-gray-400">系统负载</span>
              <span class="text-quaternary font-semibold">中等</span>
            </div>
          </div>
        </div>
      </section>
      
      <!-- 四大中枢（神经元集群） -->
      <section class="mb-24">
        <div class="flex justify-between items-center mb-10">
          <h2 class="text-[clamp(1.5rem,3vw,2.2rem)] font-bold text-white">
            <span class="text-primary">核心中枢</span> 系统
            <span class="ml-2 text-sm font-normal text-gray-400">平台端 · 神经元集群</span>
          </h2>
          <div class="text-sm text-gray-400 flex items-center">
            <i class="fa fa-refresh mr-2"></i> 数据更新于 30秒前
          </div>
        </div>
        
        <!-- 中枢连接线 -->
        <div class="hidden md:block absolute left-0 right-0 h-1 bg-gradient-to-r from-primary via-secondary to-tertiary rounded-full opacity-30"></div>
        
        <div class="relative grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 mt-6">
          <!-- AI中枢 -->
          <div class="neuron-card glass rounded-xl p-6 border border-primary/30 hover:border-primary/60 transition-all duration-300 transform hover:-translate-y-2 group relative overflow-hidden">
            <!-- 背景装饰 -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full -mr-16 -mt-16 blur-2xl"></div>
            
            <div class="relative z-10">
              <div class="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
                <i class="fa fa-microchip text-2xl text-primary glow"></i>
              </div>
              <h3 class="text-xl font-semibold mb-3 text-white flex items-center">
                AI中枢
                <span class="ml-2 text-xs bg-primary/20 text-primary px-2 py-0.5 rounded-full">核心</span>
              </h3>
              <p class="text-gray-300 text-sm mb-6">
                城市智能决策核心，通过深度学习算法分析城市运行数据，提供预测性分析和智能决策支持。
              </p>
              
              <!-- 数据指标 -->
              <div class="space-y-4">
                <div>
                  <div class="flex justify-between text-xs mb-1">
                    <span class="text-gray-400">模型准确率</span>
                    <span class="text-primary">97.2%</span>
                  </div>
                  <div class="w-full h-1.5 bg-gray-700/50 rounded-full overflow-hidden">
                    <div class="h-full bg-primary rounded-full" style="width: 97.2%"></div>
                  </div>
                </div>
                
                <div>
                  <div class="flex justify-between text-xs mb-1">
                    <span class="text-gray-400">实时分析速度</span>
                    <span class="text-primary">1,245 次/秒</span>
                  </div>
                  <div class="w-full h-1.5 bg-gray-700/50 rounded-full overflow-hidden">
                    <div class="h-full bg-primary rounded-full" style="width: 85%"></div>
                  </div>
                </div>
              </div>
              
              <div class="mt-6 pt-4 border-t border-gray-700/50 flex justify-between items-center">
                <div class="flex items-center text-xs text-primary/80">
                  <span class="w-2 h-2 rounded-full bg-green-500 animate-pulse mr-2"></span>
                  <span>运行正常</span>
                </div>
                <button class="text-xs text-primary/80 hover:text-primary transition-colors">
                  详情 <i class="fa fa-chevron-right ml-1 text-[10px]"></i>
                </button>
              </div>
            </div>
          </div>
          
          <!-- 业务中枢 -->
          <div class="neuron-card glass rounded-xl p-6 border border-secondary/30 hover:border-secondary/60 transition-all duration-300 transform hover:-translate-y-2 group relative overflow-hidden">
            <!-- 背景装饰 -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-secondary/10 rounded-full -mr-16 -mt-16 blur-2xl"></div>
            
            <div class="relative z-10">
              <div class="w-16 h-16 rounded-full bg-secondary/20 flex items-center justify-center mb-5 group-hover:bg-secondary/30 transition-colors">
                <i class="fa fa-cogs text-2xl text-secondary glow"></i>
              </div>
              <h3 class="text-xl font-semibold mb-3 text-white flex items-center">
                业务中枢
                <span class="ml-2 text-xs bg-secondary/20 text-secondary px-2 py-0.5 rounded-full">协同</span>
              </h3>
              <p class="text-gray-300 text-sm mb-6">
                整合城市各部门业务系统，实现跨部门协同办公，优化业务流程，提升城市管理效率。
              </p>
              
              <!-- 数据指标 -->
              <div class="space-y-4">
                <div>
                  <div class="flex justify-between text-xs mb-1">
                    <span class="text-gray-400">业务处理效率</span>
                    <span class="text-secondary">89.6%</span>
                  </div>
                  <div class="w-full h-1.5 bg-gray-700/50 rounded-full overflow-hidden">
                    <div class="h-full bg-secondary rounded-full" style="width: 89.6%"></div>
                  </div>
                </div>
                
                <div>
                  <div class="flex justify-between text-xs mb-1">
                    <span class="text-gray-400">部门协同率</span>
                    <span class="text-secondary">76.3%</span>
                  </div>
                  <div class="w-full h-1.5 bg-gray-700/50 rounded-full overflow-hidden">
                    <div class="h-full bg-secondary rounded-full" style="width: 76.3%"></div>
                  </div>
                </div>
              </div>
              
              <div class="mt-6 pt-4 border-t border-gray-700/50 flex justify-between items-center">
                <div class="flex items-center text-xs text-secondary/80">
                  <span class="w-2 h-2 rounded-full bg-green-500 animate-pulse mr-2"></span>
                  <span>运行正常</span>
                </div>
                <button class="text-xs text-secondary/80 hover:text-secondary transition-colors">
                  详情 <i class="fa fa-chevron-right ml-1 text-[10px]"></i>
                </button>
              </div>
            </div>
          </div>
          
          <!-- 数据中枢 -->
          <div class="neuron-card glass rounded-xl p-6 border border-tertiary/30 hover:border-tertiary/60 transition-all duration-300 transform hover:-translate-y-2 group relative overflow-hidden">
            <!-- 背景装饰 -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-tertiary/10 rounded-full -mr-16 -mt-16 blur-2xl"></div>
            
            <div class="relative z-10">
              <div class="w-16 h-16 rounded-full bg-tertiary/20 flex items-center justify-center mb-5 group-hover:bg-tertiary/30 transition-colors">
                <i class="fa fa-database text-2xl text-tertiary glow"></i>
              </div>
              <h3 class="text-xl font-semibold mb-3 text-white flex items-center">
                数据中枢
                <span class="ml-2 text-xs bg-tertiary/20 text-tertiary px-2 py-0.5 rounded-full">存储</span>
              </h3>
              <p class="text-gray-300 text-sm mb-6">
                集中管理城市PB级数据资源，确保数据安全与共享，为全系统提供数据支撑和洞察。
              </p>
              
              <!-- 数据指标 -->
              <div class="space-y-4">
                <div>
                  <div class="flex justify-between text-xs mb-1">
                    <span class="text-gray-400">数据总量</span>
                    <span class="text-tertiary">145.8 PB</span>
                  </div>
                  <div class="w-full h-1.5 bg-gray-700/50 rounded-full overflow-hidden">
                    <div class="h-full bg-tertiary rounded-full" style="width: 68%"></div>
                  </div>
                </div>
                
                <div>
                  <div class="flex justify-between text-xs mb-1">
                    <span class="text-gray-400">数据完整性</span>
                    <span class="text-tertiary">99.9%</span>
                  </div>
                  <div class="w-full h-1.5 bg-gray-700/50 rounded-full overflow-hidden">
                    <div class="h-full bg-tertiary rounded-full" style="width: 99.9%"></div>
                  </div>
                </div>
              </div>
              
              <div class="mt-6 pt-4 border-t border-gray-700/50 flex justify-between items-center">
                <div class="flex items-center text-xs text-tertiary/80">
                  <span class="w-2 h-2 rounded-full bg-green-500 animate-pulse mr-2"></span>
                  <span>运行正常</span>
                </div>
                <button class="text-xs text-tertiary/80 hover:text-tertiary transition-colors">
                  详情 <i class="fa fa-chevron-right ml-1 text-[10px]"></i>
                </button>
              </div>
            </div>
          </div>
          
          <!-- 感知中枢 -->
          <div class="neuron-card glass rounded-xl p-6 border border-quaternary/30 hover:border-quaternary/60 transition-all duration-300 transform hover:-translate-y-2 group relative overflow-hidden">
            <!-- 背景装饰 -->
            <div class="absolute top-0 right-0 w-32 h-32 bg-quaternary/10 rounded-full -mr-16 -mt-16 blur-2xl"></div>
            
            <div class="relative z-10">
              <div class="w-16 h-16 rounded-full bg-quaternary/20 flex items-center justify-center mb-5 group-hover:bg-quaternary/30 transition-colors">
                <i class="fa fa-eye text-2xl text-quaternary glow"></i>
              </div>
              <h3 class="text-xl font-semibold mb-3 text-white flex items-center">
                感知中枢
                <span class="ml-2 text-xs bg-quaternary/20 text-quaternary px-2 py-0.5 rounded-full">采集</span>
              </h3>
              <p class="text-gray-300 text-sm mb-6">
                连接城市各类感知设备，实时采集城市运行状态数据，构建城市全方位感知网络。
              </p>
              
              <!-- 数据指标 -->
              <div class="space-y-4">
                <div>
                  <div class="flex justify-between text-xs mb-1">
                    <span class="text-gray-400">设备在线率</span>
                    <span class="text-quaternary">98.4%</span>
                  </div>
                  <div class="w-full h-1.5 bg-gray-700/50 rounded-full overflow-hidden">
                    <div class="h-full bg-quaternary rounded-full" style="width: 98.4%"></div>
                  </div>
                </div>
                
                <div>
                  <div class="flex justify-between text-xs mb-1">
                    <span class="text-gray-400">数据采集频率</span>
                    <span class="text-quaternary">256 Hz</span>
                  </div>
                  <div class="w-full h-1.5 bg-gray-700/50 rounded-full overflow-hidden">
                    <div class="h-full bg-quaternary rounded-full" style="width: 92%"></div>
                  </div>
                </div>
              </div>
              
              <div class="mt-6 pt-4 border-t border-gray-700/50 flex justify-between items-center">
                <div class="flex items-center text-xs text-quaternary/80">
                  <span class="w-2 h-2 rounded-full bg-green-500 animate-pulse mr-2"></span>
                  <span>运行正常</span>
                </div>
                <button class="text-xs text-quaternary/80 hover:text-quaternary transition-colors">
                  详情 <i class="fa fa-chevron-right ml-1 text-[10px]"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 四大能力（用户端-四肢） -->
      <section class="relative">
        <div class="flex justify-between items-center mb-10">
          <h2 class="text-[clamp(1.5rem,3vw,2.2rem)] font-bold text-white">
            <span class="text-secondary">核心能力</span> 体系
            <span class="ml-2 text-sm font-normal text-gray-400">用户端 · 执行系统</span>
          </h2>
          <button class="text-sm text-gray-300 hover:text-white transition-colors flex items-center">
            <i class="fa fa-expand mr-2"></i> 全屏展示
          </button>
        </div>

        <!-- 人体躯干连接线 -->
        <div class="hidden md:block absolute left-1/2 top-0 bottom-0 w-1.5 bg-gradient-to-b from-primary via-secondary to-tertiary rounded-full transform -translate-x-1/2 opacity-50"></div>

        <!-- 能力展示区域 -->
        <div class="space-y-16 relative">
          <!-- 左臂 - AI能力 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-5/12 md:pr-12 md:text-right order-2 md:order-1 mt-6 md:mt-0">
              <div class="glass p-6 rounded-xl border border-primary/30 relative overflow-hidden">
                <!-- 背景装饰 -->
                <div class="absolute top-0 right-0 w-32 h-32 bg-primary/10 rounded-full -mr-16 -mt-16 blur-2xl"></div>

                <div class="relative z-10">
                  <h3 class="text-xl font-semibold mb-3 text-primary flex items-center justify-end">
                    <span>AI能力</span>
                    <i class="fa fa-lightbulb-o ml-2 glow"></i>
                  </h3>
                  <p class="text-gray-300 text-sm mb-4">
                    提供智能分析与决策支持，通过AI算法实现城市问题自动识别、趋势预测和最优解决方案推荐，辅助管理人员快速做出决策。
                  </p>

                  <!-- 能力指标图表 -->
                  <div class="h-40 mt-6">
                    <canvas ref="aiCapabilityChart"></canvas>
                  </div>
                </div>
              </div>
            </div>

            <!-- 连接点 -->
            <div class="md:w-2/12 flex justify-center order-1 md:order-2 relative">
              <div class="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center border-2 border-primary z-10 animate-pulse-soft">
                <div class="w-4 h-4 rounded-full bg-primary"></div>
              </div>
            </div>

            <div class="md:w-5/12 md:pl-12 order-3"></div>
          </div>

          <!-- 右臂 - 业务能力 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-5/12 md:pr-12 order-1"></div>

            <!-- 连接点 -->
            <div class="md:w-2/12 flex justify-center order-2 relative">
              <div class="w-12 h-12 rounded-full bg-secondary/20 flex items-center justify-center border-2 border-secondary z-10 animate-pulse-soft">
                <div class="w-4 h-4 rounded-full bg-secondary"></div>
              </div>
            </div>

            <div class="md:w-5/12 md:pl-12 order-3 mt-6 md:mt-0">
              <div class="glass p-6 rounded-xl border border-secondary/30 relative overflow-hidden">
                <!-- 背景装饰 -->
                <div class="absolute top-0 left-0 w-32 h-32 bg-secondary/10 rounded-full -ml-16 -mt-16 blur-2xl"></div>

                <div class="relative z-10">
                  <h3 class="text-xl font-semibold mb-3 text-secondary flex items-center">
                    <i class="fa fa-briefcase mr-2 glow"></i>
                    <span>业务能力</span>
                  </h3>
                  <p class="text-gray-300 text-sm mb-4">
                    提供一站式业务处理平台，整合各部门业务流程，实现城市管理业务线上化、协同化、智能化，提高业务处理效率和服务质量。
                  </p>

                  <!-- 业务处理统计 -->
                  <div class="grid grid-cols-2 gap-4 mt-6">
                    <div class="bg-dark/50 p-3 rounded-lg">
                      <div class="text-xs text-gray-400 mb-1">今日处理业务</div>
                      <div class="text-2xl font-bold text-secondary">2,847</div>
                      <div class="text-xs text-green-400 mt-1">
                        <i class="fa fa-arrow-up"></i> 12.3%
                      </div>
                    </div>
                    <div class="bg-dark/50 p-3 rounded-lg">
                      <div class="text-xs text-gray-400 mb-1">平均处理时长</div>
                      <div class="text-2xl font-bold text-secondary">4.2<span class="text-sm">分钟</span></div>
                      <div class="text-xs text-green-400 mt-1">
                        <i class="fa fa-arrow-down"></i> 8.7%
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 左腿 - 数据能力 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-5/12 md:pr-12 md:text-right order-2 md:order-1 mt-6 md:mt-0">
              <div class="glass p-6 rounded-xl border border-tertiary/30 relative overflow-hidden">
                <!-- 背景装饰 -->
                <div class="absolute top-0 right-0 w-32 h-32 bg-tertiary/10 rounded-full -mr-16 -mt-16 blur-2xl"></div>

                <div class="relative z-10">
                  <h3 class="text-xl font-semibold mb-3 text-tertiary flex items-center justify-end">
                    <span>数据能力</span>
                    <i class="fa fa-bar-chart ml-2 glow"></i>
                  </h3>
                  <p class="text-gray-300 text-sm mb-4">
                    提供全面的数据查询、分析与可视化服务，整合城市多维度数据资源，通过直观的数据展示帮助用户洞察城市运行规律和潜在问题。
                  </p>

                  <!-- 数据类型分布 -->
                  <div class="h-40 mt-6">
                    <canvas ref="dataDistributionChart"></canvas>
                  </div>
                </div>
              </div>
            </div>

            <!-- 连接点 -->
            <div class="md:w-2/12 flex justify-center order-1 md:order-2 relative">
              <div class="w-12 h-12 rounded-full bg-tertiary/20 flex items-center justify-center border-2 border-tertiary z-10 animate-pulse-soft">
                <div class="w-4 h-4 rounded-full bg-tertiary"></div>
              </div>
            </div>

            <div class="md:w-5/12 md:pl-12 order-3"></div>
          </div>

          <!-- 右腿 - 感知能力 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-5/12 md:pr-12 order-1"></div>

            <!-- 连接点 -->
            <div class="md:w-2/12 flex justify-center order-2 relative">
              <div class="w-12 h-12 rounded-full bg-quaternary/20 flex items-center justify-center border-2 border-quaternary z-10 animate-pulse-soft">
                <div class="w-4 h-4 rounded-full bg-quaternary"></div>
              </div>
            </div>

            <div class="md:w-5/12 md:pl-12 order-3 mt-6 md:mt-0">
              <div class="glass p-6 rounded-xl border border-quaternary/30 relative overflow-hidden">
                <!-- 背景装饰 -->
                <div class="absolute top-0 left-0 w-32 h-32 bg-quaternary/10 rounded-full -ml-16 -mt-16 blur-2xl"></div>

                <div class="relative z-10">
                  <h3 class="text-xl font-semibold mb-3 text-quaternary flex items-center">
                    <i class="fa fa-wifi mr-2 glow"></i>
                    <span>感知能力</span>
                  </h3>
                  <p class="text-gray-300 text-sm mb-4">
                    提供实时城市状态感知与监控服务，整合视频监控、传感器网络等多源感知数据，实现城市运行状态全方位、全天候监控。
                  </p>

                  <!-- 感知设备状态 -->
                  <div class="space-y-3 mt-6">
                    <div>
                      <div class="flex justify-between text-xs mb-1">
                        <span class="text-gray-400">交通监控设备</span>
                        <span class="text-quaternary">9,842 / 9,876</span>
                      </div>
                      <div class="w-full h-1.5 bg-gray-700/50 rounded-full overflow-hidden">
                        <div class="h-full bg-quaternary rounded-full" style="width: 99.6%"></div>
                      </div>
                    </div>

                    <div>
                      <div class="flex justify-between text-xs mb-1">
                        <span class="text-gray-400">环境传感器</span>
                        <span class="text-quaternary">12,451 / 12,683</span>
                      </div>
                      <div class="w-full h-1.5 bg-gray-700/50 rounded-full overflow-hidden">
                        <div class="h-full bg-quaternary rounded-full" style="width: 98.1%"></div>
                      </div>

                      <div>
                        <div class="flex justify-between text-xs mb-1">
                          <span class="text-gray-400">公共设施监测器</span>
                          <span class="text-quaternary">2,105 / 2,156</span>
                        </div>
                        <div class="w-full h-1.5 bg-gray-700/50 rounded-full overflow-hidden">
                          <div class="h-full bg-quaternary rounded-full" style="width: 97.6%"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <!-- 页脚 -->
    <footer class="relative z-10 mt-20 py-6 px-4 border-t border-gray-800/50">
      <div class="container mx-auto">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <div class="flex items-center space-x-2 mb-4 md:mb-0">
            <i class="fa fa-brain text-primary text-lg glow"></i>
            <span class="font-semibold">城市大脑IOC智能运行中心</span>
          </div>

          <div class="text-sm text-gray-400">
            &copy; 2025 城市智能管理系统 版权所有 | 版本 v1.0.0
          </div>

          <div class="flex space-x-4 mt-4 md:mt-0">
            <button class="text-gray-400 hover:text-primary transition-colors">
              <i class="fa fa-question-circle"></i>
            </button>
            <button class="text-gray-400 hover:text-primary transition-colors">
              <i class="fa fa-cog"></i>
            </button>
            <button class="text-gray-400 hover:text-primary transition-colors">
              <i class="fa fa-sign-out"></i>
            </button>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { Chart, registerables } from 'chart.js'

Chart.register(...registerables)

const aiCapabilityChart = ref<HTMLCanvasElement>()
const dataDistributionChart = ref<HTMLCanvasElement>()

onMounted(() => {
  // 为中枢卡片添加滚动动画
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('opacity-100', 'translate-y-0')
        entry.target.classList.remove('opacity-0', 'translate-y-10')
      }
    })
  }, { threshold: 0.1 })

  // 为所有神经元卡片添加初始样式和观察器
  document.querySelectorAll('.neuron-card').forEach(card => {
    card.classList.add('transition-all', 'duration-1000', 'opacity-0', 'translate-y-10')
    observer.observe(card)
  })

  // 配置图表颜色
  const chartColors = {
    primary: 'rgba(14, 165, 233, 0.8)',
    secondary: 'rgba(139, 92, 246, 0.8)',
    tertiary: 'rgba(16, 185, 129, 0.8)',
    quaternary: 'rgba(245, 158, 11, 0.8)',
    background: 'rgba(30, 41, 59, 0.6)',
    grid: 'rgba(255, 255, 255, 0.1)'
  }

  // 创建AI能力图表
  if (aiCapabilityChart.value) {
    const aiCtx = aiCapabilityChart.value.getContext('2d')
    if (aiCtx) {
      new Chart(aiCtx, {
        type: 'line',
        data: {
          labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
          datasets: [{
            label: 'AI决策准确率',
            data: [89.2, 91.5, 93.1, 94.7, 96.3, 97.2],
            borderColor: chartColors.primary,
            backgroundColor: 'rgba(14, 165, 233, 0.1)',
            tension: 0.4,
            fill: true
          }, {
            label: '问题识别效率',
            data: [78.5, 82.3, 85.7, 88.9, 91.2, 93.5],
            borderColor: chartColors.secondary,
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              labels: {
                color: 'rgba(255, 255, 255, 0.7)'
              }
            }
          },
          scales: {
            y: {
              beginAtZero: false,
              min: 70,
              grid: {
                color: chartColors.grid
              },
              ticks: {
                color: 'rgba(255, 255, 255, 0.5)',
                callback: function(value) {
                  return value + '%'
                }
              }
            },
            x: {
              grid: {
                color: chartColors.grid
              },
              ticks: {
                color: 'rgba(255, 255, 255, 0.5)'
              }
            }
          }
        }
      })
    }
  }

  // 创建数据分布图表
  if (dataDistributionChart.value) {
    const dataCtx = dataDistributionChart.value.getContext('2d')
    if (dataCtx) {
      new Chart(dataCtx, {
        type: 'doughnut',
        data: {
          labels: ['交通数据', '环境数据', '公共服务', '能源消耗', '安全监控'],
          datasets: [{
            data: [35, 20, 15, 18, 12],
            backgroundColor: [
              chartColors.tertiary,
              chartColors.primary,
              chartColors.secondary,
              chartColors.quaternary,
              'rgba(239, 68, 68, 0.7)'
            ],
            borderWidth: 0,
            hoverOffset: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          cutout: '70%',
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                color: 'rgba(255, 255, 255, 0.7)',
                padding: 15,
                boxWidth: 10,
                boxHeight: 10
              }
            }
          }
        }
      })
    }
  }

  // 模拟数据更新动画
  setInterval(() => {
    const pulses = document.querySelectorAll('.animate-pulse')
    pulses.forEach(pulse => {
      pulse.classList.remove('animate-pulse')
      setTimeout(() => pulse.classList.add('animate-pulse'), 10)
    })
  }, 5000)
})
</script>

<style scoped>
/* 导入字体 */
@import url('https://fonts.googleapis.com/css2?family=Rajdhani:wght@300;400;500;600;700&display=swap');

/* 自定义工具类 */
.content-auto {
  content-visibility: auto;
}

.brain-connection {
  stroke-dasharray: 15;
  animation: dash 8s linear infinite;
}

.nerve-pulse {
  stroke-dasharray: 10;
  animation: dash 2s linear infinite;
}

.glow {
  filter: drop-shadow(0 0 8px currentColor);
}

.text-shadow {
  text-shadow: 0 0 8px currentColor;
}

.glass {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

@keyframes dash {
  to {
    stroke-dashoffset: 100;
  }
}

/* 自定义动画 */
@keyframes flicker {
  0%, 19.999%, 22%, 62.999%, 64%, 64.999%, 70%, 100% {
    opacity: 1;
  }
  20%, 21.999%, 63%, 63.999%, 65%, 69.999% {
    opacity: 0.4;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-soft {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-flicker {
  animation: flicker 3s linear infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-soft {
  animation: pulse-soft 2s infinite;
}
</style>
